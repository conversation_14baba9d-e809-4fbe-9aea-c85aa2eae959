#!/usr/bin/env python3
"""
Script spécialisé pour convertir les fichiers NDE Suzlon en images PNG.
Ce script détecte automatiquement l'orientation optimale pour générer de vraies endviews
au lieu de sideviews pour les fichiers avec structure Domain.
"""

import os
import sys
import argparse
import h5py
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

def detect_and_correct_negatives(data):
    """
    Détecte et corrige automatiquement les valeurs négatives en les ramenant à zéro

    Returns:
        tuple: (données_corrigées, correction_appliquée, info_correction)
    """
    min_val = np.min(data)

    if min_val >= 0:
        return data, False, "Aucune correction nécessaire"

    # Compter les valeurs négatives
    negative_mask = data < 0
    negative_count = np.sum(negative_mask)
    negative_percentage = (negative_count / data.size) * 100

    # Simple : ramener toutes les valeurs négatives à zéro
    corrected_data = np.clip(data, 0, None)
    correction_info = f"Clipping à 0 appliqué ({negative_count} valeurs négatives, {negative_percentage:.3f}%)"

    return corrected_data, True, correction_info

def normalize_data_safely(data, min_value, max_value):
    """
    Normalise les données de manière sécurisée en évitant les débordements
    """
    # Convertir en float64 pour éviter les débordements
    data_float = data.astype(np.float64)
    min_float = float(min_value)
    max_float = float(max_value)

    # Éviter la division par zéro
    if max_float == min_float:
        return np.zeros_like(data_float)

    # Normalisation sécurisée
    normalized = (data_float - min_float) / (max_float - min_float)

    # Clipper entre 0 et 1
    normalized = np.clip(normalized, 0.0, 1.0)

    return normalized

def detect_suzlon_endview_orientation(data_array, verbose=False):
    """
    Détecte l'orientation optimale pour les endviews des fichiers Suzlon.
    
    Args:
        data_array: Array 3D (lengthwise, crosswise, ultrasound)
        verbose: Afficher les détails de détection
    
    Returns:
        dict: {'slice_orientation': str, 'transpose': bool}
    """
    lengthwise_qty, crosswise_qty, ultrasound_qty = data_array.shape
    
    # Test des 3 orientations possibles
    orientations = []
    
    # 1. Slice lengthwise: data[idx,:,:] → (crosswise, ultrasound)
    slice_lengthwise = data_array[0,:,:]
    aspect_lengthwise = slice_lengthwise.shape[1] / slice_lengthwise.shape[0] if slice_lengthwise.shape[0] > 0 else 1.0
    orientations.append({
        'name': 'lengthwise',
        'shape': slice_lengthwise.shape,
        'aspect': aspect_lengthwise,
        'description': 'crosswise × ultrasound'
    })
    
    # 2. Slice crosswise: data[:,idx,:] → (lengthwise, ultrasound)
    slice_crosswise = data_array[:,0,:]
    aspect_crosswise = slice_crosswise.shape[1] / slice_crosswise.shape[0] if slice_crosswise.shape[0] > 0 else 1.0
    orientations.append({
        'name': 'crosswise',
        'shape': slice_crosswise.shape,
        'aspect': aspect_crosswise,
        'description': 'lengthwise × ultrasound'
    })
    
    # 3. Slice ultrasound: data[:,:,idx] → (lengthwise, crosswise)
    slice_ultrasound = data_array[:,:,0]
    aspect_ultrasound = slice_ultrasound.shape[1] / slice_ultrasound.shape[0] if slice_ultrasound.shape[0] > 0 else 1.0
    orientations.append({
        'name': 'ultrasound',
        'shape': slice_ultrasound.shape,
        'aspect': aspect_ultrasound,
        'description': 'lengthwise × crosswise'
    })
    
    if verbose:
        print(f"Détection d'orientation pour shape {data_array.shape}:")
        for orient in orientations:
            print(f"  {orient['name']}: {orient['shape']} ({orient['description']}), aspect {orient['aspect']:.2f}")
    
    # Système de score pour choisir la meilleure orientation
    # Privilégier le nombre d'images généré (plus d'images = mieux)
    best_orientation = None
    best_score = -1

    for orient in orientations:
        aspect = orient['aspect']

        # Calculer le nombre d'images qui seraient générées
        if orient['name'] == 'lengthwise':
            num_images = lengthwise_qty
        elif orient['name'] == 'crosswise':
            num_images = crosswise_qty
        else:  # ultrasound
            num_images = ultrasound_qty

        # Score basé sur le nombre d'images (plus d'images = mieux)
        # et aspect ratio raisonnable
        if num_images >= 1000:
            score = 20  # Beaucoup d'images = excellent
        elif num_images >= 500:
            score = 15  # Bon nombre d'images
        elif num_images >= 100:
            score = 10  # Nombre acceptable
        else:
            score = 5   # Peu d'images

        # Bonus/malus pour aspect ratio
        if 0.1 <= aspect <= 50.0:  # Très large gamme acceptable
            score += 2
        elif 0.05 <= aspect <= 100.0:  # Gamme élargie
            score += 1
        # Pas de malus pour les aspect ratios extrêmes

        if verbose:
            print(f"    {orient['name']}: {num_images} images, aspect {aspect:.3f}, score {score}")

        if score > best_score:
            best_score = score
            best_orientation = orient
    
    # Détermine si transpose est nécessaire
    transpose = best_orientation['aspect'] < 1.0
    
    if verbose:
        print(f"Orientation sélectionnée: {best_orientation['name']} avec transpose={transpose}")
        final_shape = best_orientation['shape'] if not transpose else (best_orientation['shape'][1], best_orientation['shape'][0])
        print(f"Shape finale: {final_shape}")
    
    return {
        'slice_orientation': best_orientation['name'],
        'transpose': transpose
    }

def extract_suzlon_slice(data_array, idx, slice_orientation, total_slices):
    """
    Extrait le slice approprié selon l'orientation détectée.

    Args:
        data_array: Array 3D des données
        idx: Index du slice à extraire
        slice_orientation: 'lengthwise', 'crosswise', ou 'ultrasound'
        total_slices: Nombre total de slices à générer

    Returns:
        numpy.ndarray: Le slice extrait
    """
    if slice_orientation == 'crosswise':
        # Extraire directement le slice crosswise
        return data_array[:, idx, :]

    elif slice_orientation == 'ultrasound':
        # Extraire directement le slice ultrasound
        return data_array[:, :, idx]

    else:  # lengthwise (traditionnel)
        return data_array[idx, :, :]

def safe_division(numerator, denominator):
    """Division sécurisée pour éviter la division par zéro."""
    return np.divide(numerator, denominator, out=np.zeros_like(numerator, dtype=float), where=denominator!=0)

def generate_suzlon_endview_image(img_data, min_value, max_value, colorize=True,
                                 path_omniscancolormap='./OmniScanColorMap.npy',
                                 apply_rotation=True):
    """
    Génère une image endview avec le même traitement que le script original.

    Args:
        img_data: Données de l'image (2D array)
        min_value: Valeur minimale pour la normalisation
        max_value: Valeur maximale pour la normalisation
        colorize: True pour RGB, False pour grayscale
        path_omniscancolormap: Chemin vers la colormap

    Returns:
        PIL.Image: Image générée
    """
    # Normaliser les données de manière sécurisée
    img_data_normalized = normalize_data_safely(img_data, min_value, max_value)

    # Charger la colormap
    if os.path.exists(path_omniscancolormap):
        OmniScanColorMap = np.load(path_omniscancolormap)
        omniscan_cmap = ListedColormap(OmniScanColorMap)
    else:
        # Fallback vers une colormap par défaut si le fichier n'existe pas
        omniscan_cmap = plt.cm.viridis

    # Générer l'image selon le mode
    if colorize:
        # Version RGB24
        img = Image.fromarray(np.uint8(omniscan_cmap(img_data_normalized) * 255))
    else:
        # Version uint8 grayscale
        img = Image.fromarray(np.uint8(img_data_normalized * 255), 'L')

    # Appliquer rotation 90° anti-horaire si demandée
    if apply_rotation:
        img = img.transpose(Image.ROTATE_270)

    return img

def process_suzlon_nde_file(nde_file, output_dir, verbose=False,
                           path_omniscancolormap='./OmniScanColorMap.npy',
                           apply_transpose=True, apply_rotation=True):
    """
    Traite un fichier NDE Suzlon et génère les images PNG dans les formats uint8 et rgb24.

    Args:
        nde_file: Chemin vers le fichier .nde
        output_dir: Répertoire de sortie
        verbose: Afficher les détails
        path_omniscancolormap: Chemin vers la colormap OmniScan
        apply_transpose: Appliquer la transposition (.T) aux données avant traitement
        apply_rotation: Appliquer rotation 90° anti-horaire aux images
    """
    print(f"Traitement du fichier Suzlon: {nde_file}")

    with h5py.File(nde_file, 'r') as f:
        # Vérifier la structure Domain (spécifique Suzlon)
        if 'Domain' not in f.keys():
            print("Erreur: Ce script est conçu pour les fichiers avec structure Domain (Suzlon)")
            return False

        # Extraire les données
        data_path = "Domain/DataGroups/0/Datasets/0/Amplitude"
        if data_path not in f:
            print(f"Erreur: Chemin de données non trouvé: {data_path}")
            return False

        data_array = f[data_path][:]
        print(f"Shape des données: {data_array.shape}")

        # Détecter et corriger automatiquement les valeurs négatives
        original_min = np.min(data_array)
        original_max = np.max(data_array)
        print(f"Valeurs fichier: min={original_min}, max={original_max}")

        data_array, correction_applied, correction_info = detect_and_correct_negatives(data_array)

        if correction_applied:
            print(f"🔧 Correction automatique: {correction_info}")
            corrected_min = np.min(data_array)
            corrected_max = np.max(data_array)
            print(f"Valeurs après correction: min={corrected_min}, max={corrected_max}")

        # Calculer min/max pour la normalisation
        min_value = np.min(data_array)
        max_value = np.max(data_array)

        # Détecter l'orientation optimale
        config = detect_suzlon_endview_orientation(data_array, verbose=verbose)
        slice_orientation = config['slice_orientation']
        transpose = config['transpose']

        # Créer les répertoires de sortie (comme dans le script original)
        uint8_dir = os.path.join(output_dir, 'endviews_uint8', 'complete')
        rgb24_dir = os.path.join(output_dir, 'endviews_rgb24', 'complete')
        os.makedirs(uint8_dir, exist_ok=True)
        os.makedirs(rgb24_dir, exist_ok=True)

        # Générer les images selon l'orientation détectée
        lengthwise_qty, crosswise_qty, ultrasound_qty = data_array.shape

        if slice_orientation == 'lengthwise':
            total_slices = lengthwise_qty
        elif slice_orientation == 'crosswise':
            total_slices = crosswise_qty
        else:  # ultrasound
            total_slices = ultrasound_qty

        print(f"Génération de {total_slices} endviews...")
        print(f"  - Version uint8 (grayscale) dans: {uint8_dir}")
        print(f"  - Version rgb24 (colorisée) dans: {rgb24_dir}")

        for idx in range(total_slices):
            # Extraire le slice
            img_data = extract_suzlon_slice(data_array, idx, slice_orientation, total_slices)

            # Appliquer transpose de détection d'orientation si nécessaire
            if transpose:
                img_data = img_data.T

            # Appliquer transpose utilisateur si demandé (comme dans le script original)
            if apply_transpose:
                img_data = img_data.T

            # Calculer la position (simulée pour la compatibilité)
            position = idx / (total_slices - 1) if total_slices > 1 else 0
            position_filename = int(position * 1000 * 1000)

            # Générer les deux versions d'images
            filename = f"endview_{position_filename:012d}.png"

            # Version uint8 (grayscale)
            img_uint8 = generate_suzlon_endview_image(img_data, min_value, max_value,
                                                     colorize=False,
                                                     path_omniscancolormap=path_omniscancolormap,
                                                     apply_rotation=apply_rotation)
            img_uint8.save(os.path.join(uint8_dir, filename))

            # Version rgb24 (colorisée)
            img_rgb24 = generate_suzlon_endview_image(img_data, min_value, max_value,
                                                     colorize=True,
                                                     path_omniscancolormap=path_omniscancolormap,
                                                     apply_rotation=apply_rotation)
            img_rgb24.save(os.path.join(rgb24_dir, filename))

            if idx % 10 == 0:
                print(f"  Généré {idx+1}/{total_slices} images...")

        print(f"✅ Terminé! {total_slices} images sauvegardées dans chaque format")
        print(f"   Orientation utilisée: {slice_orientation}")
        print(f"   Transpose détection: {transpose}")
        print(f"   Transpose utilisateur: {apply_transpose}")
        print(f"   Rotation 90° anti-horaire: {'Oui' if apply_rotation else 'Non'}")
        print(f"   Shape finale des images: {img_data.shape}")

        return True

def main():
    parser = argparse.ArgumentParser(description="Convertir fichiers NDE Suzlon en images PNG avec orientation optimale")
    parser.add_argument("--file", required=True, help="Chemin vers le fichier .nde Suzlon")
    parser.add_argument("--output", required=True, help="Répertoire de sortie pour les images")
    parser.add_argument("--verbose", "-v", action="store_true", help="Affichage détaillé")
    parser.add_argument("--colormap", default="./OmniScanColorMap.npy",
                       help="Chemin vers le fichier colormap OmniScan (défaut: ./OmniScanColorMap.npy)")
    parser.add_argument("--no-transpose", action="store_true",
                       help="Désactiver la transposition des données (activée par défaut)")
    parser.add_argument("--no-rotation", action="store_true",
                       help="Désactiver la rotation 90° anti-horaire (activée par défaut)")

    args = parser.parse_args()

    if not os.path.exists(args.file):
        print(f"Erreur: Fichier non trouvé: {args.file}")
        sys.exit(1)

    success = process_suzlon_nde_file(args.file, args.output, args.verbose, args.colormap,
                                     apply_transpose=not args.no_transpose,
                                     apply_rotation=not args.no_rotation)

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
