# Nouvelle Fonctionnalité : Conversion Automatique pour Sentinel

## Description

Une nouvelle option a été ajoutée à l'interface graphique pour le script `pngs_to_volume.py` qui permet d'appliquer automatiquement la conversion pour Sentinel après la création du volume.

## Fonctionnalité

### Dans l'interface graphique

Lorsque vous utilisez le script "PNGs vers volume" dans l'interface graphique, vous trouverez maintenant une nouvelle case à cocher :

**☑️ Appliquer la conversion pour Sentinel (convertir 'volume' → 'arr_0')**

### Comportement

1. **Case décochée (par défaut)** : Le script `pngs_to_volume.py` fonctionne normalement et crée un fichier volume avec la clé standard.

2. **Case cochée** : 
   - Le script `pngs_to_volume.py` s'exécute normalement
   - **Si le format de sortie est NPZ**, le script `convertir_volume_pour_sentinel.py` est automatiquement appliqué au fichier créé
   - La clé 'volume' est convertie en 'arr_0' (format attendu par Sentinel)
   - **Si le format n'est pas NPZ**, un avertissement est affiché et la conversion Sentinel est ignorée

## Scripts modifiés

### 1. `convertir_volume_pour_sentinel.py`
- **Avant** : Script avec chemin codé en dur
- **Après** : Script avec arguments en ligne de commande
- **Usage** : `python convertir_volume_pour_sentinel.py <fichier_ou_dossier>`
- **Fonctionnalités** :
  - Accepte un fichier NPZ unique ou un dossier contenant des fichiers NPZ
  - Convertit la clé 'volume' en 'arr_0'
  - Gestion d'erreurs améliorée
  - Messages d'état clairs

### 2. `interface_graphique.py`
- Ajout de la variable `apply_sentinel_conversion` (BooleanVar)
- Ajout de la case à cocher dans l'interface
- Modification de `run_python_script()` pour supporter les callbacks
- Ajout de `_post_pngs_to_volume()` pour gérer la conversion post-traitement

## Utilisation

### Via l'interface graphique
1. Lancez l'interface : `python interface_graphique.py`
2. Sélectionnez "PNGs vers volume"
3. Configurez vos paramètres normalement
4. **Cochez la case "Appliquer la conversion pour Sentinel"** si vous voulez la conversion automatique
5. Assurez-vous que le format de sortie est "npz"
6. Cliquez sur "Exécuter"

### Via la ligne de commande
```bash
# Conversion normale
python pngs_to_volume.py dossier_pngs fichier_sortie --format npz

# Puis conversion Sentinel séparée
python convertir_volume_pour_sentinel.py fichier_sortie.npz
```

## Exemple de test

Un script de démonstration est fourni :
```bash
python demo_pngs_to_volume_avec_sentinel.py
```

Ce script :
- Crée un dossier `demo_pngs` avec des images de test
- Affiche les instructions pour tester la fonctionnalité
- Fournit les commandes de vérification

## Vérification du résultat

Après conversion, vous pouvez vérifier que la clé a bien été changée :

```python
import numpy as np
z = np.load('votre_fichier.npz')
print("Clés disponibles :", list(z.files))
# Devrait afficher : ['arr_0'] au lieu de ['volume']
```

## Notes importantes

- ⚠️ **La conversion Sentinel ne fonctionne qu'avec le format NPZ**
- ⚠️ **La conversion écrase le fichier original** (la clé 'volume' est remplacée par 'arr_0')
- ✅ **La fonctionnalité est optionnelle** : décochez la case pour le comportement normal
- ✅ **Compatible avec tous les paramètres existants** de `pngs_to_volume.py`

## Compatibilité

Cette modification est entièrement rétrocompatible :
- Les scripts existants continuent de fonctionner normalement
- L'option est désactivée par défaut
- Aucun changement dans les autres fonctionnalités de l'interface
