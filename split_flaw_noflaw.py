#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import shutil
from pathlib import Path

# === CHEMINS HARDCODÉS ===
images_dir = Path(r"C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153019\SB70-MGO-153019-10M-15M-05-07-2024_0148\endviews_rgb24\complete")
masks_dir  = images_dir / "masks_binary"
txt_path   = masks_dir / "view_labels_results_20250919_130232.txt"
out_root   = images_dir.parent / "nnunet_split"   # sortie au même niveau que "endviews_rgb24"

# === Dossiers de sortie ===
labels_flaw    = out_root / "labelsTr_flaw"
labels_noflaw  = out_root / "labelsTr_noflaw"
images_flaw    = out_root / "imagesTr_flaw"
images_noflaw  = out_root / "imagesTr_noflaw"

for d in [labels_flaw, labels_noflaw, images_flaw, images_noflaw]:
    d.mkdir(parents=True, exist_ok=True)

LINE_RE = re.compile(r'^(?P<name>[^:]+):\s*\[(?P<classes>[^\]]*)\]\s*$')

def parse_label_file(txt_path: Path):
    mapping = {}
    with txt_path.open('r', encoding='utf-8', errors='ignore') as f:
        for raw in f:
            line = raw.strip()
            m = LINE_RE.match(line)
            if not m:
                continue
            name = m.group('name').strip()
            classes_str = m.group('classes').strip()
            cls_set = set()
            if classes_str:
                for tok in classes_str.split(','):
                    tok = tok.strip()
                    if tok.lstrip('-').isdigit():
                        cls_set.add(int(tok))
            mapping[name] = cls_set
    return mapping

def decide_bucket(cls_set):
    if cls_set == {0}:
        return "noflaw"
    if 1 in cls_set or len(cls_set) >= 2:
        return "flaw"
    return "noflaw"

def safe_copy(src: Path, dst_dir: Path):
    if src.is_file():
        shutil.copy2(src, dst_dir / src.name)
    else:
        print(f"[WARN] fichier manquant: {src}")

def main():
    mapping = parse_label_file(txt_path)
    stats = {"labels_flaw":0,"labels_noflaw":0,"images_flaw":0,"images_noflaw":0}

    for fname, cls_set in mapping.items():
        bucket = decide_bucket(cls_set)

        mask_path = masks_dir / fname
        img_path  = images_dir / fname

        if bucket == "flaw":
            safe_copy(mask_path, labels_flaw)
            safe_copy(img_path, images_flaw)
            stats["labels_flaw"] += 1
            stats["images_flaw"] += 1
        else:
            safe_copy(mask_path, labels_noflaw)
            safe_copy(img_path, images_noflaw)
            stats["labels_noflaw"] += 1
            stats["images_noflaw"] += 1

    print("\n=== Résumé ===")
    for k,v in stats.items():
        print(f"{k}: {v}")

if __name__ == "__main__":
    main()
