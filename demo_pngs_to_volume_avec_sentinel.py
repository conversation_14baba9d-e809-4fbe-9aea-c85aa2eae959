#!/usr/bin/env python3
"""
Script de démonstration pour tester la nouvelle fonctionnalité
pngs_to_volume avec conversion Sentinel automatique
"""
import numpy as np
import os
from pathlib import Path
from PIL import Image

def create_demo_pngs(output_dir="demo_pngs", num_images=5):
    """Crée un dossier avec quelques images PNG de démonstration"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    print(f"[INFO] Création de {num_images} images PNG de démonstration dans {output_path}")
    
    # Créer des images de test simples
    for i in range(num_images):
        # Créer une image avec un gradient
        img_array = np.zeros((100, 100), dtype=np.uint8)
        
        # Ajouter un motif simple
        for y in range(100):
            for x in range(100):
                img_array[y, x] = (x + y + i * 50) % 256
        
        # Sauvegarder l'image
        img = Image.fromarray(img_array, mode='L')
        img_path = output_path / f"slice_{i:03d}.png"
        img.save(img_path)
    
    print(f"[OK] {num_images} images créées dans {output_path}")
    return output_path

def demo_conversion():
    """Démonstration de la conversion complète"""
    print("=" * 60)
    print("DÉMONSTRATION : PNGs vers Volume avec conversion Sentinel")
    print("=" * 60)
    
    # Créer les images de démonstration
    png_folder = create_demo_pngs()
    
    print(f"\n[INFO] Dossier PNG créé : {png_folder}")
    print(f"[INFO] Fichiers PNG : {list(png_folder.glob('*.png'))}")
    
    print("\n" + "=" * 60)
    print("ÉTAPES À SUIVRE DANS L'INTERFACE GRAPHIQUE :")
    print("=" * 60)
    print("1. Lancez l'interface graphique : python interface_graphique.py")
    print("2. Sélectionnez 'PNGs vers volume' dans la liste des scripts")
    print("3. Configurez les paramètres :")
    print(f"   - Dossier PNG : {png_folder.absolute()}")
    print(f"   - Fichier sortie : {png_folder.absolute()}/demo_volume")
    print("   - Format de sortie : npz")
    print("   - Mode couleur : L")
    print("   - Ordre des axes : ZYX")
    print("   - ✅ COCHEZ la case 'Appliquer la conversion pour Sentinel'")
    print("4. Cliquez sur 'Exécuter'")
    print("\n[INFO] Le script va :")
    print("   a) Convertir les PNGs en volume NPZ avec clé 'volume'")
    print("   b) Automatiquement convertir la clé 'volume' en 'arr_0' (format Sentinel)")
    
    print("\n" + "=" * 60)
    print("VÉRIFICATION MANUELLE (optionnelle) :")
    print("=" * 60)
    print("Après l'exécution, vous pouvez vérifier le résultat avec :")
    print(f"python -c \"import numpy as np; z=np.load('{png_folder}/demo_volume.npz'); print('Clés:', list(z.files)); print('Shape:', z['arr_0'].shape)\"")
    
    return png_folder

if __name__ == "__main__":
    demo_folder = demo_conversion()
    
    print(f"\n[INFO] Dossier de démonstration prêt : {demo_folder.absolute()}")
    print("[INFO] Vous pouvez maintenant tester la fonctionnalité dans l'interface graphique !")
