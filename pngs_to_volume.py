#!/usr/bin/env python3
"""
Script pour convertir un dossier de PNGs en un volume 3D et sauvegarder dans le format choisi (npz, h5, ou nifti).
"""
import os
import numpy as np
from glob import glob
from PIL import Image
import argparse
import sys
from pathlib import Path

class PNGToVolumeConverter:
    def __init__(self, png_folder, output_path, output_format='h5', color_mode='L', axis_order='ZYX'):
        """
        Initialise le convertisseur PNG vers volume

        Args:
            png_folder: Dossier contenant les images PNG
            output_path: Chemin de sortie (sans extension)
            output_format: Format de sortie ('npz', 'h5', 'nifti')
            color_mode: Mode couleur ('L' pour grayscale, 'RGB' pour couleur)
            axis_order: Ordre des axes ('ZYX', 'XYZ', etc.)
        """
        self.png_folder = Path(png_folder)
        self.output_path = Path(output_path)
        self.output_format = output_format.lower()
        self.color_mode = color_mode
        self.axis_order = axis_order.upper()

        # Vérifications
        if not self.png_folder.exists():
            raise FileNotFoundError(f"Le dossier {png_folder} n'existe pas")

        if self.output_format not in ['npz', 'h5', 'nifti']:
            raise ValueError("Format de sortie doit être 'npz', 'h5' ou 'nifti'")

        if self.color_mode not in ['L', 'RGB']:
            raise ValueError("Mode couleur doit être 'L' (grayscale) ou 'RGB'")

    def convert(self):
        """Effectue la conversion complète"""
        print(f"[INFO] Conversion PNG vers volume")
        print(f"[INFO] Dossier source : {self.png_folder}")
        print(f"[INFO] Fichier sortie : {self.output_path}")
        print(f"[INFO] Format : {self.output_format.upper()}")
        print(f"[INFO] Mode couleur : {self.color_mode}")
        print(f"[INFO] Ordre des axes : {self.axis_order}")

        # Charger les PNGs
        volume = self.load_png_stack()

        # Réorganiser les axes si nécessaire
        volume = self.reorder_axes(volume)

        # Sauvegarder
        self.save_volume(volume)

        print(f"[OK] Conversion terminee avec succes !")
        return True

    def load_png_stack(self):
        """Charge tous les PNGs d'un dossier, triés, et les empile en un volume 3D."""
        png_files = sorted(self.png_folder.glob('*.png'))
        if not png_files:
            raise FileNotFoundError(f"Aucun PNG trouve dans {self.png_folder}")

        print(f"[INFO] {len(png_files)} fichiers PNG trouves")

        slices = []
        for i, png_file in enumerate(png_files):
            if i % 50 == 0:  # Afficher le progrès
                print(f"[PROGRESS] Chargement : {i+1}/{len(png_files)} ({(i+1)/len(png_files)*100:.1f}%)")

            img = Image.open(png_file).convert(self.color_mode)
            slices.append(np.array(img))

        volume = np.stack(slices, axis=0)
        print(f"[INFO] Volume cree : {volume.shape} ({self.axis_order})")
        print(f"[INFO] Type de donnees : {volume.dtype}")
        print(f"[INFO] Taille memoire : {volume.nbytes / (1024*1024):.2f} MB")

        return volume

    def reorder_axes(self, volume):
        """Réorganise les axes selon l'ordre spécifié

        Volume original : (Z, Y, X) où Z=nombre d'images, Y=hauteur, X=largeur
        """
        # Dictionnaire des transpositions possibles
        # Format: 'ordre_souhaité': (axe_0, axe_1, axe_2)
        transpose_map = {
            'ZYX': (0, 1, 2),  # Ordre par défaut - pas de transposition
            'ZXY': (0, 2, 1),  # Z, X, Y
            'YZX': (1, 0, 2),  # Y, Z, X
            'YXZ': (1, 2, 0),  # Y, X, Z
            'XZY': (2, 0, 1),  # X, Z, Y
            'XYZ': (2, 1, 0),  # X, Y, Z
        }

        if self.axis_order in transpose_map:
            transpose_order = transpose_map[self.axis_order]
            if transpose_order == (0, 1, 2):
                return volume  # Pas de transposition nécessaire
            else:
                return np.transpose(volume, transpose_order)
        else:
            print(f"[WARN] Ordre d'axes '{self.axis_order}' non supporte, utilisation de ZYX")
            print(f"[INFO] Ordres supportes : {', '.join(transpose_map.keys())}")
            return volume

    def save_volume(self, volume):
        """Sauvegarde le volume dans le format spécifié"""
        if self.output_format == 'npz':
            self._save_npz(volume)
        elif self.output_format == 'h5':
            self._save_h5(volume)
        elif self.output_format == 'nifti':
            self._save_nifti(volume)
        else:
            raise ValueError(f"Format de sortie non supporté : {self.output_format}")

    def _save_npz(self, volume):
        """Sauvegarde au format NPZ"""
        output_file = self.output_path.with_suffix('.npz')
        np.savez_compressed(output_file, volume=volume)
        print(f"[OK] Volume sauvegarde au format NPZ : {output_file}")

    def _save_h5(self, volume):
        """Sauvegarde au format H5"""
        try:
            import h5py
        except ImportError:
            raise ImportError("h5py n'est pas installe. Installez-le avec : pip install h5py")

        output_file = self.output_path.with_suffix('.h5')
        with h5py.File(output_file, 'w') as f:
            f.create_dataset('volume', data=volume, compression='gzip')
        print(f"[OK] Volume sauvegarde au format H5 : {output_file}")

    def _save_nifti(self, volume):
        """Sauvegarde au format NIfTI"""
        try:
            import nibabel as nib
        except ImportError:
            raise ImportError("nibabel n'est pas installe. Installez-le avec : pip install nibabel")

        output_file = self.output_path.with_suffix('.nii.gz')
        nifti_img = nib.Nifti1Image(volume, affine=np.eye(4))
        nib.save(nifti_img, output_file)
        print(f"[OK] Volume sauvegarde au format NIfTI : {output_file}")

def main():
    """Fonction principale avec arguments en ligne de commande"""
    parser = argparse.ArgumentParser(description="Convertit un dossier de PNGs en volume 3D")
    parser.add_argument("png_folder", help="Dossier contenant les images PNG")
    parser.add_argument("output_path", help="Chemin de sortie (sans extension)")
    parser.add_argument("--format", choices=['npz', 'h5', 'nifti'], default='h5',
                       help="Format de sortie (défaut: h5)")
    parser.add_argument("--color-mode", choices=['L', 'RGB'], default='L',
                       help="Mode couleur : L (grayscale) ou RGB (défaut: L)")
    parser.add_argument("--axis-order", choices=['ZYX', 'ZXY', 'YZX', 'YXZ', 'XZY', 'XYZ'], default='ZYX',
                       help="Ordre des axes - ZYX: (profondeur,hauteur,largeur), XZY: (largeur,profondeur,hauteur), etc. (défaut: ZYX)")

    args = parser.parse_args()

    try:
        # Créer le convertisseur
        converter = PNGToVolumeConverter(
            png_folder=args.png_folder,
            output_path=args.output_path,
            output_format=args.format,
            color_mode=args.color_mode,
            axis_order=args.axis_order
        )

        # Effectuer la conversion
        converter.convert()

    except Exception as e:
        print(f"[ERROR] Erreur lors de la conversion : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()